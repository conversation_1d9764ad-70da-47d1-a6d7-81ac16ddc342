# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2025 Frank<PERSON>Lo<PERSON>
# This file is distributed under the same license as the Notion to WordPress package.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Notion to WordPress 2.0.0-beta.1\n"
"Report-Msgid-Bugs-To: https://github.com/Frank-<PERSON><PERSON>/Notion-to-WordPress/issues\n"
"POT-Creation-Date: 2025-01-08 17:30+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: admin\class-notion-to-wordpress-admin.php
msgid "导入中..."
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "手动导入"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "导入过程中发生错误"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "测试中..."
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "测试连接"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "测试连接时发生错误"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "已复制到剪贴板"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "刷新中..."
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "刷新验证令牌"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "统计信息错误"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "确定要开始同步Notion内容吗？"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "确定要刷新全部内容吗？这将根据Notion的当前状态重新同步所有页面。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "确定要清除所有日志文件吗？此操作不可恢复。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "请填写所有必填字段"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "隐藏密钥"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "显示密钥"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "从未"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "未计划"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "未知错误"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "页面ID无效，无法刷新。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "安全验证参数缺失，无法继续操作。请刷新页面后重试。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "页面已刷新完成！"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "刷新失败: "
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "网络错误，无法刷新页面。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "操作超时，请检查该Notion页面内容是否过大。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "请先选择一个日志文件。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "正在加载日志..."
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "无法加载日志: "
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "请求日志时发生错误。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "复制失败: 未指定目标元素"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "复制失败: 未找到目标元素"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "复制失败: "
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "复制到剪贴板"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "复制代码"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "已复制!"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "复制失败，请手动复制。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "加载中..."
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "加载统计信息..."
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "无法加载统计信息"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "刷新全部内容"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "刷新失败"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "清除中..."
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "清除所有日志"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "设置已保存！"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "保存中..."
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "智能同步"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "完全同步"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "确定要执行智能同步吗？（仅同步有变化的内容）"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "确定要执行完全同步吗？（同步所有内容，耗时较长）"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "中..."
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "完成"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "失败，请稍后重试"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "页面即将刷新以应用设置变更..."
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion to WordPress"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "安全验证失败"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "权限不足"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "设置已成功保存。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "保存设置时发生错误："
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "导入完成！处理了 %d 个页面，导入了 %d 个页面，更新了 %d 个页面。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
#: includes\class-notion-pages.php
msgid "导入失败: "
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "请先配置API密钥和数据库ID"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "刷新完成！处理了 %d 个页面，导入了 %d 个页面，更新了 %d 个页面。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "未指定日志文件"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "验证令牌已刷新"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "🛠️ 主要设置"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "🔗 字段映射"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "⚙️ 其他设置"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "🐞 调试工具"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "📖 帮助与指南"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "👨‍💻 关于作者"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion API 设置"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "已导入页面"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "已发布页面"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "最后同步"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "下次同步"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "连接到您的Notion数据库所需的设置。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "了解如何获取API密钥"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "API密钥"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "输入您的Notion API密钥"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "显示/隐藏密钥"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "数据库ID"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "输入您的Notion数据库ID"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "可以从Notion数据库URL中找到，格式如：https://www.notion.so/xxx/<strong>数据库ID</strong>?v=xxx"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "自动同步频率"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "手动同步"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "每天两次"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "每天一次"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
#: includes\class-notion-to-wordpress.php
msgid "每周一次"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
#: includes\class-notion-to-wordpress.php
msgid "每两周一次"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
#: includes\class-notion-to-wordpress.php
msgid "每月一次"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "定时同步选项"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "启用增量同步"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "仅同步有变化的页面，提高同步速度"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "检查删除的页面"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "自动删除在Notion中已删除但WordPress中仍存在的文章"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Webhook 支持"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "启用 Webhook 支持"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "启用后，您可以设置 Notion 集成的 Webhook 以在内容变更时自动触发同步。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "验证令牌"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "等待 Notion 返回…"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "复制令牌"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "首次发送 Webhook 时，Notion 将返回 verification_token，此处会自动展示。点击刷新按钮可获取最新的令牌。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Webhook 地址"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "复制 URL"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在 Notion 开发者平台设置此 URL 作为您集成的 Webhook 终端点。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Webhook 同步选项"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Webhook触发时仅同步有变化的页面，提高响应速度"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "数据库事件检查删除"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "数据库结构变化时检查删除的页面（单页面事件不受影响）"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "字段映射"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "设置您的Notion数据库属性名称与WordPress字段的对应关系。多个备选名称请用英文逗号隔开。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "文章标题"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章标题的Notion属性名称"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "状态"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "文章类型"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于确定WordPress文章类型的Notion属性名称"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "日期"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章发布日期的Notion属性名称"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "摘要"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章摘要的Notion属性名称"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "特色图片"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress特色图片的Notion属性名称（应为URL或文件类型）"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "分类"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章分类的Notion属性名称"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "标签"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章标签的Notion属性名称"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "文章密码"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章密码的Notion属性名称"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "自定义字段映射"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "将Notion属性映射到WordPress自定义字段。您可以添加任意数量的自定义字段映射。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "文本"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "数字"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "复选框"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "选择"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "多选"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "URL"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "电子邮件"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "电话"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "富文本"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion属性名称"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "例如：Author,作者"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion中的属性名称，多个备选名称请用英文逗号分隔"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "WordPress字段名称"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "例如：author"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "WordPress中的自定义字段名称"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "字段类型"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion属性的数据类型"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "添加自定义字段"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "其他设置"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "卸载设置"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "卸载时删除所有同步内容"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "卸载插件时，删除所有从Notion同步的文章和页面"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "警告：此操作不可逆！所有通过Notion同步的内容将被永久删除。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "iframe 白名单域名"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "允许在内容中嵌入的 iframe 域名白名单，多个域名请用英文逗号分隔。输入 * 表示允许所有域名（不推荐）。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "允许的图片格式"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "允许下载和导入的图片 MIME 类型，多个类型请用英文逗号分隔。输入 * 表示允许所有格式。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "插件界面语言"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "自动检测（跟随站点语言）"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "简体中文"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "English"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "选择插件界面显示的语言。自动检测将跟随WordPress站点语言设置。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "最大图片大小"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "MB"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "允许下载的最大图片大小（以 MB 为单位）。建议不超过 10MB。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "调试工具"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在这里，您可以管理日志级别、查看和清除日志文件。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "日志记录级别"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "无日志"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "仅错误"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "信息和错误"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "所有日志 (调试)"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "日志保留期限"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "从不自动清理"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "7 天"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "14 天"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "30 天"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "60 天"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "错误日志"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "查看日志"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "使用帮助"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "快速开始"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在Notion创建一个集成并获取API密钥"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在Notion中创建一个数据库，并与您的集成共享"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "复制数据库ID（从URL中获取）"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在此页面配置API密钥和数据库ID"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "配置字段映射，确保Notion属性名称与WordPress字段正确对应"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "常见问题"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "问：为什么我的Notion页面没有导入？"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "答：请检查以下几点："
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "确认您的API密钥和数据库ID正确"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "确认您的Notion集成已与数据库共享"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "检查字段映射是否正确对应Notion中的属性名称"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "问：如何自定义导入的内容格式？"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "答：本插件会尽可能保留Notion中的格式，包括标题、列表、表格、代码块等。对于特殊内容（如数学公式、图表），插件也提供了支持。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "问：导入后如何更新内容？"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "获取支持"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "如果您遇到任何问题或需要帮助，请访问我们的GitHub仓库："
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "关于作者"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "科技爱好者 & AI玩家"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "对互联网、计算机等科技行业充满热情，擅长 AI 工具的使用与调教。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "此插件在强大的 AI 编程助手 Cursor 和 Augment 的协助下完成，现在将这个有趣的项目分享给大家。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "个人网站"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "联系邮箱"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "插件信息"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "版本："
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "许可证："
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "兼容性："
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "致谢与参考"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "本项目的开发过程中参考了以下优秀的开源项目："
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "基于 Notion 的强大静态博客系统"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "支持多平台的开源博客写作客户端"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion 内容管理解决方案"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "感谢这些项目及其维护者对开源社区的贡献！"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "只同步有变化的页面，速度更快"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "同步所有页面，确保数据一致性"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "保存所有设置"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "处理中，请稍候..."
msgstr ""

#: includes\class-notion-api.php
msgid "API请求失败: "
msgstr ""

#: includes\class-notion-api.php
msgid "API错误 ("
msgstr ""

#: includes\class-notion-api.php
msgid "连接测试失败: "
msgstr ""

#: includes\class-notion-pages.php
msgid "此为 Notion 临时图片链接，可能会过期。请考虑替换为图床或本地媒体库图片。"
msgstr ""

#: includes\class-notion-pages.php
msgid "未检索到任何页面。"
msgstr ""

#: includes\class-notion-pages.php
msgid "下载附件"
msgstr ""

#: includes\class-notion-pages.php
msgid "下载 PDF"
msgstr ""

#: includes\class-notion-pages.php
msgid "在新窗口打开"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "Token mismatch"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "无效的请求体"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "缺少事件类型"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "Webhook已接收，正在处理"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "同步过程中出错: "
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "已触发%s"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "页面ID为空，无法处理删除事件"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "未找到对应的WordPress文章"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "已删除对应的WordPress文章 (ID: %d)"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "删除WordPress文章失败"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "已同步页面: %s"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "页面同步失败"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "单页面同步失败，已执行%s"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "Notion Pages对象不存在"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "获取页面数据失败"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "已强制同步页面内容: %s"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "页面内容同步失败"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "页面内容同步失败，已执行%s"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "页面内容同步发生致命错误"
msgstr ""

#: includes\class-notion-to-wordpress-webhook.php
msgid "数据库已更新，已触发%s%s"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "设置日志记录的详细程度。建议在生产环境中设置为"仅错误"。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "自动删除超过指定天数的旧日志文件。设置为"从不"以禁用。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "点击"测试连接"确认设置正确"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "点击"保存所有设置"保存您的配置"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "点击"手动同步"或设置自动同步频率开始导入内容"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "尝试使用"刷新全部内容"按钮重新同步"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "答：当您在Notion中更新内容后，可以点击"刷新全部内容"按钮手动更新，或等待自动同步（如果已设置）。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "选择 "手动同步" 以禁用定时任务。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "要复制的文本为空"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "暂无新的验证令牌"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "请输入API密钥和数据库ID"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "连接成功！数据库可访问。"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "连接失败: "
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "缺少nonce参数"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "nonce验证失败"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "Nonce验证失败"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "获取统计信息失败: "
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "获取统计信息错误: "
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "所有日志文件已清除"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "清除日志时出现错误"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "清除日志失败: "
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "无效"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "不存在"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "调试测试成功"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "测试失败: "
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "测试错误: "
msgstr ""

#: includes\class-notion-pages.php
msgid "无效的嵌入URL"
msgstr ""

#: includes\class-notion-pages.php
msgid "无效的视频URL"
msgstr ""

#: includes\class-notion-pages.php
msgid "您的浏览器不支持视频标签。"
msgstr ""

#: includes\class-notion-pages.php
msgid "查看视频"
msgstr ""

#: includes\class-notion-pages.php
msgid "不允许的图片类型："
msgstr ""

#: includes\class-notion-pages.php
msgid "图片文件过大（%sMB），超过限制（%sMB）"
msgstr ""

#: includes\class-notion-pages.php
msgid "无效的 PDF URL"
msgstr ""

#: includes\class-notion-pages.php
msgid "您的浏览器不支持PDF预览。"
msgstr ""

#: includes\class-notion-pages.php
msgid "点击下载PDF文件"
msgstr ""

#: includes\class-notion-pages.php
msgid "PDF 下载失败"
msgstr ""

#: includes\class-notion-pages.php
msgid "下载附件失败: "
msgstr ""

#: includes\class-notion-pages.php
msgid "无效的PDF文件或包含不安全内容"
msgstr ""

#: includes\class-notion-pages.php
msgid "media_handle_sideload 错误: "
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "内容已截断，完整内容请查看专用日志文件"
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "HTML标签已过滤"
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "HTML内容已过滤"
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "JSON响应已过滤，长度: %d 字符"
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "数组内容已过滤，长度: %d 字符"
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "无效的文件名。"
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "日志文件不存在。"
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "日志清理任务跳过：未设置保留期限。"
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "日志清理完成，删除了 %d 个旧日志文件。"
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "日志清理任务运行，没有需要删除的文件。"
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "无效的URL"
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "远程请求失败: "
msgstr ""

#: includes\class-notion-to-wordpress-helper.php
msgid "HTTP错误 %d: %s"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "详细信息"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "验证令牌已更新"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "语言设置"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "Webhook设置"
msgstr ""

#: admin\class-notion-to-wordpress-admin.php
msgid "和"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在Notion的"我的集成"页面创建并获取API密钥。"
msgstr ""

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "值为 "Published" 或 "已发布" 的页面会被设为 "已发布" 状态，其他则为 "草稿"。"
msgstr ""

#: includes\class-notion-pages.php
msgid "暂无记录"
msgstr ""

#: includes\class-notion-pages.php
msgid "无标题"
msgstr ""

#: includes\class-notion-pages.php
msgid "封面图片"
msgstr ""

#: includes\class-notion-pages.php
msgid "图标"
msgstr ""

#: includes\class-notion-pages.php
msgid "数据库记录封面"
msgstr ""

#: includes\class-notion-pages.php
msgid "数据库记录图标"
msgstr ""

#: includes\class-notion-pages.php
msgid "加载更多记录 (%d)"
msgstr ""

#: includes\class-notion-pages.php
msgid "标题"
msgstr ""

#: includes\class-notion-pages.php
msgid "记录ID不能为空"
msgstr ""

#: includes\class-notion-pages.php
msgid "无法获取记录详情"
msgstr ""

#: includes\class-notion-pages.php
msgid "获取记录详情失败: %s"
msgstr ""

