<?php
declare(strict_types=1);

namespace NTWP\Infrastructure\Concurrency;

use NTWP\Core\Foundation\Logger;

/**
 * 统一并发管理器
 * 
 * 功能整合:
 * ✅ 网络请求并发 (from Concurrent_Network_Manager)
 * ✅ 配置管理 (from Unified_Concurrency_Manager)
 * ✅ 动态调优 (from Dynamic_Concurrency_Manager)
 */
class ConcurrencyManager {
    
    private array $config = [
        'max_concurrent_requests' => 5,
        'request_timeout' => 30,
        'enable_adaptive_adjustment' => true,
        'memory_threshold' => 0.8,
        'cpu_threshold' => 2.0,
        'min_concurrency' => 1,
        'max_concurrency' => 10,
    ];
    
    private $multi_handle;
    private array $performance_metrics = [];
    
    /**
     * 数据量预估缓存
     * @var array
     */
    private array $size_estimation_cache = [];
    
    /**
     * 并发统计信息
     * @var array
     */
    private array $concurrency_stats = [
        'total_batches_processed' => 0,
        'adaptive_adjustments' => 0,
        'optimal_concurrency_history' => [],
        'performance_improvements' => 0,
    ];
    
    public function __construct() {
        $this->multi_handle = curl_multi_init();
        $this->load_config();
    }
    
    /**
     * 执行并发请求 - 核心功能
     * 
     * @param array $requests 请求配置数组
     * @return array 响应结果
     */
    public function execute_concurrent_requests(array $requests): array {
        if (empty($requests)) {
            return [];
        }
        
        $start_time = microtime(true);
        $optimal_concurrency = $this->get_optimal_concurrency();
        
        Logger::debug_log(
            sprintf('开始并发处理 %d 个请求，并发数: %d', 
                count($requests), $optimal_concurrency),
            'ConcurrencyManager'
        );
        
        $results = [];
        $batches = array_chunk($requests, $optimal_concurrency);
        
        foreach ($batches as $batch) {
            $batch_results = $this->process_batch($batch);
            $results = array_merge($results, $batch_results);
            
            // 性能自适应调整
            if ($this->config['enable_adaptive_adjustment']) {
                $this->adjust_concurrency_based_on_performance();
            }
        }
        
        $total_time = microtime(true) - $start_time;
        $this->record_performance_metrics($total_time, count($requests));
        
        return $results;
    }
    
    /**
     * 计算最优并发数 - 动态调优核心
     * 
     * @return int 最优并发数
     */
    public function get_optimal_concurrency(): int {
        $base_concurrency = $this->config['max_concurrent_requests'];
        
        if (!$this->config['enable_adaptive_adjustment']) {
            return $base_concurrency;
        }
        
        // 系统资源检查
        $system_load = sys_getloadavg()[0] ?? 1.0;
        $memory_usage = memory_get_usage(true) / $this->get_memory_limit();
        
        $adjustment_factor = 1.0;
        
        // CPU负载调整
        if ($system_load > $this->config['cpu_threshold']) {
            $adjustment_factor *= 0.7; // 减少30%
        }
        
        // 内存使用调整
        if ($memory_usage > $this->config['memory_threshold']) {
            $adjustment_factor *= 0.8; // 减少20%
        }
        
        // 历史性能调整
        if (!empty($this->performance_metrics)) {
            $avg_response_time = array_sum($this->performance_metrics) / count($this->performance_metrics);
            if ($avg_response_time > 3.0) {
                $adjustment_factor *= 0.9; // 响应慢则减少并发
            }
        }
        
        $optimal = max(1, intval($base_concurrency * $adjustment_factor));
        
        return min($this->config['max_concurrent_requests'], $optimal);
    }
    
    /**
     * 处理单个批次 - cURL multi-handle核心逻辑
     */
    private function process_batch(array $batch): array {
        $curl_handles = [];
        $results = [];
        
        // 初始化cURL句柄
        foreach ($batch as $index => $request) {
            $ch = curl_init();
            curl_setopt_array($ch, $this->prepare_curl_options($request));
            curl_multi_add_handle($this->multi_handle, $ch);
            $curl_handles[$index] = $ch;
        }
        
        // 执行并发请求
        $running = null;
        do {
            $status = curl_multi_exec($this->multi_handle, $running);
            if ($running > 0) {
                curl_multi_select($this->multi_handle, 0.1);
            }
        } while ($running > 0 && $status === CURLM_OK);
        
        // 收集结果
        foreach ($curl_handles as $index => $ch) {
            $response = curl_multi_getcontent($ch);
            $info = curl_getinfo($ch);
            $error = curl_error($ch);
            
            $results[$index] = [
                'response' => $response,
                'http_code' => $info['http_code'],
                'response_time' => $info['total_time'],
                'error' => $error,
                'success' => empty($error) && $info['http_code'] < 400
            ];
            
            curl_multi_remove_handle($this->multi_handle, $ch);
            curl_close($ch);
        }
        
        return $results;
    }
    
    /**
     * 准备cURL选项
     */
    private function prepare_curl_options(array $request): array {
        $default_options = [
            CURLOPT_URL => $request['url'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['request_timeout'],
            CURLOPT_FOLLOWLOCATION => false,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_USERAGENT => 'Notion-to-WordPress/2.0',
            CURLOPT_HTTPHEADER => $request['headers'] ?? [],
        ];
        
        if (!empty($request['curl_options'])) {
            $default_options = array_replace($default_options, $request['curl_options']);
        }
        
        return $default_options;
    }
    
    /**
     * 连接池管理 (from Concurrent_Network_Manager)
     * @var array
     */
    private array $connection_pool = [];
    private array $connection_pool_stats = [
        'total_connections_created' => 0,
        'connections_reused' => 0,
        'connections_closed' => 0,
        'pool_hits' => 0,
        'pool_misses' => 0
    ];
    
    /**
     * 任务管理统计 (from Unified_Concurrency_Manager)
     * @var array
     */
    private static array $task_counters = [
        'requests' => 0,
        'downloads' => 0,
        'uploads' => 0
    ];
    
    /**
     * 配置管理
     */
    public function configure_limits(array $config): void {
        $validated_config = $this->validate_config($config);
        $this->config = array_merge($this->config, $validated_config);
        
        Logger::debug_log('并发配置已更新: ' . json_encode($validated_config), 'ConcurrencyManager');
    }
    
    /**
     * 性能监控 - 增强版
     */
    public function monitor_performance(): array {
        return $this->get_detailed_performance_stats();
    }
    
    // === 辅助方法 ===
    
    private function load_config(): void {
        $options = get_option('notion_to_wordpress_options', []);
        
        $this->config = array_merge($this->config, [
            'max_concurrent_requests' => $options['concurrent_requests'] ?? 5,
            'enable_adaptive_adjustment' => $options['enable_adaptive_concurrency'] ?? true,
        ]);
    }
    
    private function adjust_concurrency_based_on_performance(): void {
        if (count($this->performance_metrics) < 3) return; // 需要足够样本
        
        $recent_avg = array_sum(array_slice($this->performance_metrics, -3)) / 3;
        
        if ($recent_avg > 3.0) {
            // 性能下降，减少并发
            $this->config['max_concurrent_requests'] = max(1, 
                intval($this->config['max_concurrent_requests'] * 0.8)
            );
        } elseif ($recent_avg < 1.0) {
            // 性能良好，适度增加
            $this->config['max_concurrent_requests'] = min(10,
                intval($this->config['max_concurrent_requests'] * 1.2)
            );
        }
    }
    
    private function record_performance_metrics(float $total_time, int $request_count): void {
        $avg_time = $total_time / $request_count;
        $this->performance_metrics[] = $avg_time;
        
        // 只保留最近50次记录
        if (count($this->performance_metrics) > 50) {
            array_shift($this->performance_metrics);
        }
    }
    
    private function validate_config(array $config): array {
        $validated = [];
        
        if (isset($config['max_concurrent_requests'])) {
            $validated['max_concurrent_requests'] = max(1, min(20, intval($config['max_concurrent_requests'])));
        }
        
        if (isset($config['request_timeout'])) {
            $validated['request_timeout'] = max(5, min(300, intval($config['request_timeout'])));
        }
        
        if (isset($config['memory_threshold'])) {
            $validated['memory_threshold'] = max(0.5, min(0.95, floatval($config['memory_threshold'])));
        }
        
        return $validated;
    }
    
    private function get_memory_limit(): int {
        $limit = ini_get('memory_limit');
        if ($limit === '-1') return PHP_INT_MAX;
        
        $unit = strtoupper(substr($limit, -1));
        $value = intval($limit);
        
        return match($unit) {
            'G' => $value * 1024 * 1024 * 1024,
            'M' => $value * 1024 * 1024, 
            'K' => $value * 1024,
            default => $value
        };
    }
    
    
    /**
     * 适配性并发管理方法 - 根据数据量动态调整
     *
     * @param array $requests 请求配置数组
     * @param int $estimated_size 预估数据量
     * @return array 响应结果
     */
    public function execute_adaptive_concurrent_requests(array $requests, int $estimated_size = 0): array {
        if (empty($requests)) {
            return [];
        }
        
        $start_time = microtime(true);
        
        // 智能并发数计算
        $optimal_concurrency = $this->calculate_optimal_concurrency_by_data_size(
            $estimated_size > 0 ? $estimated_size : count($requests)
        );
        
        Logger::debug_log(
            sprintf('适配性并发处理: %d 个请求，预估大小: %d，最优并发数: %d', 
                count($requests), $estimated_size, $optimal_concurrency),
            'ConcurrencyManager'
        );
        
        $results = [];
        $batches = array_chunk($requests, $optimal_concurrency);
        $this->concurrency_stats['total_batches_processed'] += count($batches);
        
        foreach ($batches as $batch_index => $batch) {
            $batch_results = $this->process_adaptive_batch($batch, $batch_index);
            $results = array_merge($results, $batch_results);
            
            // 实时性能监控和自适应调整
            if ($this->config['enable_adaptive_adjustment']) {
                $new_optimal = $this->adjust_concurrency_based_on_real_time_performance($optimal_concurrency, $batch_results);
                if ($new_optimal !== $optimal_concurrency) {
                    $optimal_concurrency = $new_optimal;
                    $this->concurrency_stats['adaptive_adjustments']++;
                    Logger::debug_log(
                        sprintf('实时调整并发数: %d -> %d', $optimal_concurrency, $new_optimal),
                        'ConcurrencyManager'
                    );
                }
            }
        }
        
        $total_time = microtime(true) - $start_time;
        $this->record_adaptive_performance_metrics($total_time, count($requests), $optimal_concurrency);
        
        return $results;
    }

    /**
     * 根据数据量计算最优并发数 - 智能算法
     *
     * @param int $estimated_size 预估的数据量
     * @param int $page_size 每页大小
     * @return int 最优并发数
     */
    public function calculate_optimal_concurrency_by_data_size(int $estimated_size, int $page_size = 100): int {
        // 计算预估的页面数
        $estimated_pages = ceil($estimated_size / $page_size);
        
        // 基础并发数计算
        $base_concurrency = $this->config['max_concurrent_requests'];
        
        // 根据数据量动态调整并发数
        if ($estimated_pages <= 2) {
            $optimal_concurrency = 1; // 小数据集使用单线程
        } elseif ($estimated_pages <= 10) {
            $optimal_concurrency = min(3, $estimated_pages); // 中等数据集
        } else {
            $optimal_concurrency = min($this->config['max_concurrent_requests'], ceil($estimated_pages / 5)); // 大数据集
        }
        
        // 考虑系统负载调整
        $system_load = $this->get_system_load_factor();
        $memory_factor = $this->get_memory_usage_factor();
        
        $adjustment_factor = min($system_load, $memory_factor);
        $optimal_concurrency = max(
            $this->config['min_concurrency'],
            intval($optimal_concurrency * $adjustment_factor)
        );
        
        // 历史性能调整
        if (!empty($this->performance_metrics)) {
            $avg_response_time = array_sum($this->performance_metrics) / count($this->performance_metrics);
            if ($avg_response_time > 3.0) {
                $optimal_concurrency = max($this->config['min_concurrency'], intval($optimal_concurrency * 0.8));
            } elseif ($avg_response_time < 1.0) {
                $optimal_concurrency = min($this->config['max_concurrency'], intval($optimal_concurrency * 1.2));
            }
        }
        
        // 记录历史数据
        $this->concurrency_stats['optimal_concurrency_history'][] = [
            'timestamp' => time(),
            'estimated_size' => $estimated_size,
            'optimal_concurrency' => $optimal_concurrency,
            'system_load' => $system_load,
            'memory_factor' => $memory_factor,
        ];
        
        // 只保留最近50条记录
        if (count($this->concurrency_stats['optimal_concurrency_history']) > 50) {
            array_shift($this->concurrency_stats['optimal_concurrency_history']);
        }
        
        Logger::debug_log(
            sprintf(
                '智能并发计算: 预估大小=%d, 页面数=%d, 系统负载=%.2f, 内存因子=%.2f, 最优并发=%d',
                $estimated_size,
                $estimated_pages,
                $system_load,
                $memory_factor,
                $optimal_concurrency
            ),
            'ConcurrencyManager'
        );
        
        return $optimal_concurrency;
    }

    /**
     * 预估数据库大小
     *
     * @param string $database_id 数据库ID
     * @param array $filter 过滤条件
     * @return int 预估的页面数量
     */
    public function estimate_database_size(string $database_id, array $filter = []): int {
        $cache_key = md5($database_id . serialize($filter));
        
        // 检查缓存
        if (isset($this->size_estimation_cache[$cache_key])) {
            return $this->size_estimation_cache[$cache_key];
        }
        
        // 执行小样本查询来预估大小
        $sample_size = 10;
        $estimation = $sample_size; // 默认预估值
        
        try {
            // 这里可以实现更复杂的预估逻辑
            // 比如查询数据库的元数据或执行小样本查询
            
            // 简化实现：根据过滤条件调整预估
            if (empty($filter)) {
                $estimation = 500; // 无过滤条件时的默认预估
            } else {
                $estimation = 100; // 有过滤条件时的预估
            }
            
            // 缓存预估结果
            $this->size_estimation_cache[$cache_key] = $estimation;
            
        } catch (\Exception $e) {
            Logger::warning_log(
                sprintf('数据库大小预估失败: %s', $e->getMessage()),
                'ConcurrencyManager'
            );
        }
        
        return $estimation;
    }

    /**
     * 批量任务管理
     *
     * @param string $type 任务类型
     * @param int $task_count 任务数量
     * @return array 批量管理信息
     */
    public function manage_batch_tasks(string $type, int $task_count): array {
        $optimal_concurrency = $this->calculate_optimal_concurrency_by_data_size($task_count);
        $batch_size = min($optimal_concurrency, $task_count);
        $batches = ceil($task_count / $batch_size);
        
        return [
            'optimal_concurrency' => $optimal_concurrency,
            'batch_size' => $batch_size,
            'total_batches' => $batches,
            'estimated_time' => $batches * 2, // 估算时间（秒）
            'recommendation' => match(true) {
                $batches > 20 => 'consider_splitting_further',
                $batches > 10 => 'consider_splitting',
                default => 'proceed'
            }
        ];
    }
    
    // === 新增的辅助方法 ===
    
    /**
     * 获取系统负载因子
     */
    private function get_system_load_factor(): float {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg()[0] ?? 1.0;
            if ($load > $this->config['cpu_threshold']) {
                return 0.7; // 高负载降低并发
            } elseif ($load < 0.5) {
                return 1.3; // 低负载增加并发
            }
        }
        return 1.0;
    }
    
    /**
     * 获取内存使用因子
     */
    private function get_memory_usage_factor(): float {
        $memory_usage = memory_get_usage(true) / $this->get_memory_limit();
        
        if ($memory_usage > $this->config['memory_threshold']) {
            return 0.8; // 内存紧张时减少并发
        } elseif ($memory_usage < 0.5) {
            return 1.2; // 内存充裕时适度增加
        }
        
        return 1.0;
    }
    
    /**
     * 处理适配性批次
     */
    private function process_adaptive_batch(array $batch, int $batch_index): array {
        $batch_start_time = microtime(true);
        $results = $this->process_batch($batch);
        $batch_time = microtime(true) - $batch_start_time;
        
        // 记录批次性能
        $successful_count = count(array_filter($results, fn($r) => $r['success'] ?? false));
        $batch_performance = [
            'batch_index' => $batch_index,
            'batch_size' => count($batch),
            'execution_time' => $batch_time,
            'success_rate' => $successful_count / count($batch),
            'avg_response_time' => $batch_time / count($batch),
        ];
        
        Logger::debug_log(
            sprintf('Batch %d: 耗时%.2fs, 成功率%.1f%%, 平均响应%.3fs',
                $batch_index, $batch_time, $batch_performance['success_rate'] * 100, $batch_performance['avg_response_time']),
            'ConcurrencyManager'
        );
        
        return $results;
    }
    
    /**
     * 基于实时性能调整并发数
     */
    private function adjust_concurrency_based_on_real_time_performance(int $current_concurrency, array $batch_results): int {
        $success_count = count(array_filter($batch_results, fn($r) => $r['success'] ?? false));
        $success_rate = $success_count / count($batch_results);
        
        // 根据成功率调整
        if ($success_rate < 0.8) {
            // 成功率低，减少并发
            return max($this->config['min_concurrency'], intval($current_concurrency * 0.8));
        } elseif ($success_rate > 0.95) {
            // 成功率高，可以适度增加并发
            return min($this->config['max_concurrency'], intval($current_concurrency * 1.1));
        }
        
        return $current_concurrency;
    }
    
    /**
     * 记录适配性性能指标
     */
    private function record_adaptive_performance_metrics(float $total_time, int $request_count, int $optimal_concurrency): void {
        $avg_time = $total_time / $request_count;
        $this->performance_metrics[] = $avg_time;
        
        // 只保留最近50次记录
        if (count($this->performance_metrics) > 50) {
            array_shift($this->performance_metrics);
        }
        
        // 记录性能改进
        if (count($this->performance_metrics) >= 10) {
            $recent_avg = array_sum(array_slice($this->performance_metrics, -5)) / 5;
            $historical_avg = array_sum(array_slice($this->performance_metrics, -10, 5)) / 5;
            
            if ($recent_avg < $historical_avg * 0.9) {
                $this->concurrency_stats['performance_improvements']++;
            }
        }
    }
    
    /**
     * 获取详细性能统计
     */
    public function get_detailed_performance_stats(): array {
        return [
            'current_concurrency' => $this->config['max_concurrent_requests'],
            'performance_metrics' => $this->performance_metrics,
            'concurrency_stats' => $this->concurrency_stats,
            'system_resources' => [
                'memory_usage' => memory_get_usage(true),
                'memory_limit' => $this->get_memory_limit(),
                'memory_usage_percentage' => (memory_get_usage(true) / $this->get_memory_limit()) * 100,
                'system_load' => function_exists('sys_getloadavg') ? sys_getloadavg()[0] : 'N/A',
            ],
            'connection_pool_stats' => class_exists('\NTWP\Core\HttpClient') ? 
                \NTWP\Core\HttpClient::get_connection_pool_stats() : [],
            'cache_stats' => [
                'size_estimation_cache_size' => count($this->size_estimation_cache),
            ],
        ];
    }
    
    // ========================================
    // 从原始并发管理器中恢复的高级功能
    // ========================================
    
    /**
     * 任务管理 - 统一并发管理功能 (from Unified_Concurrency_Manager)
     */
    public function can_start_task(string $type): bool {
        $max_concurrent = $this->get_max_concurrent_for_type($type);
        $current_count = self::$task_counters[$type] ?? 0;
        
        return $current_count < $max_concurrent && $this->is_system_healthy();
    }
    
    public function start_task(string $type): bool {
        if (!$this->can_start_task($type)) {
            return false;
        }
        
        self::$task_counters[$type] = (self::$task_counters[$type] ?? 0) + 1;
        
        Logger::debug_log(
            sprintf('开始任务: %s (当前: %d)', $type, self::$task_counters[$type]),
            'ConcurrencyManager'
        );
        
        return true;
    }
    
    public function end_task(string $type): void {
        if (isset(self::$task_counters[$type]) && self::$task_counters[$type] > 0) {
            self::$task_counters[$type]--;
            
            Logger::debug_log(
                sprintf('结束任务: %s (当前: %d)', $type, self::$task_counters[$type]),
                'ConcurrencyManager'
            );
        }
    }
    
    public function is_system_healthy(): bool {
        $system_load = sys_getloadavg()[0] ?? 1.0;
        $memory_usage = memory_get_usage(true) / $this->get_memory_limit();
        
        return $system_load < $this->config['cpu_threshold'] && 
               $memory_usage < $this->config['memory_threshold'];
    }
    
    public function get_optimal_concurrency_for_type(string $type, int $data_size = 0): int {
        $base_concurrency = $this->get_max_concurrent_for_type($type);
        
        // 根据数据大小调整
        if ($data_size > 1000) {
            return max(1, intval($base_concurrency * 0.7));
        } elseif ($data_size > 100) {
            return max(1, intval($base_concurrency * 0.9));
        }
        
        return $base_concurrency;
    }
    
    public function get_concurrency_stats(): array {
        return [
            'task_counters' => self::$task_counters,
            'system_healthy' => $this->is_system_healthy(),
            'optimal_concurrency' => $this->get_optimal_concurrency(),
            'performance_stats' => $this->concurrency_stats
        ];
    }
    
    private function get_max_concurrent_for_type(string $type): int {
        $defaults = [
            'requests' => $this->config['max_concurrent_requests'],
            'downloads' => max(1, intval($this->config['max_concurrent_requests'] * 0.6)),
            'uploads' => max(1, intval($this->config['max_concurrent_requests'] * 0.4))
        ];
        
        return $defaults[$type] ?? $this->config['max_concurrent_requests'];
    }
}