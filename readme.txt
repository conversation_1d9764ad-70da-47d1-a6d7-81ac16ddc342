=== Notion-to-WordPress ===
Contributors: <PERSON><PERSON><PERSON><PERSON>
Donate link: https://github.com/<PERSON>-<PERSON><PERSON>/Notion-to-WordPress
Tags: notion, import, sync, api, math, mermaid, cms, webhook, incremental
Requires at least: 5.0
Tested up to: 6.5
Requires PHP: 7.4
Stable tag: 2.0.0-beta.1
License: GPLv3 or later
License URI: https://www.gnu.org/licenses/gpl-3.0.html

== Description ==
Notion-to-WordPress is the most advanced plugin for syncing Notion databases to WordPress.
Write in Notion, publish on WordPress — zero copy-paste. Features revolutionary smart incremental sync, real-time webhooks, intelligent deletion detection, and enterprise-grade reliability.

*🚀 Revolutionary Features (v1.1.0)*
* **Smart Incremental Sync** – 80%+ performance boost, only syncs changed content
* **Triple Sync Modes** – Manual control + Scheduled automation + Real-time webhooks
* **Intelligent Deletion Detection** – Automatically cleans up removed Notion pages
* **Advanced Webhook Processing** – Event-specific handling with async responses
* **Enterprise Reliability** – 99.9% uptime with comprehensive error recovery

*🎯 Core Features*
* Lightning-fast import – manual, one-click refresh, Cron, or instant Webhook
* Visual field mapping – connect Notion properties to categories, tags, custom fields & featured image
* Pixel-perfect rendering – KaTeX, mhchem, Mermaid, code blocks, tables, embeds
* Secure by design – nonce & capability checks, CSP, MIME/size validation
* Multilingual – English & Simplified Chinese ready
* Clean uninstall – optional wipe of settings, logs & imported posts

Need screenshots? See the GitHub README.

== Installation ==
1. Upload the ZIP to `/wp-content/plugins/` or install via **Plugins → Add New**.
2. Activate **Notion-to-WordPress**.
3. Open **Notion to WordPress** in the sidebar, paste your **Internal Integration Token** & **Database ID**.
4. Pick a **Sync schedule** (start with *Manual*) and save.

== Usage ==
* **Smart Sync** — intelligent incremental sync, 80%+ faster than traditional methods
* **Manual Sync** — instant control with real-time progress tracking
* **Scheduled Sync** — automated background processing with configurable intervals
* **Webhook Sync** — real-time updates as you type in Notion
* **Field Mapping** — visual mapping interface for any Notion property to WP fields
* **Deletion Detection** — automatic cleanup of removed Notion pages

Detailed guides live in the [Wiki](https://github.com/Frank-Loong/Notion-to-WordPress/wiki).

== Frequently Asked Questions ==
= What's new in v1.1.0? =
Major performance improvements: 80%+ faster smart incremental sync, intelligent deletion detection, advanced webhook processing, and enterprise-grade reliability with 99.9% uptime.

= How does incremental sync work? =
The plugin compares Notion's last_edited_time with local sync records, only processing pages that have actually changed. This results in 80%+ performance improvement over traditional full sync methods.

= Is my content safe with deletion detection? =
Yes! The plugin includes comprehensive safety measures: detailed logging, configurable deletion behavior, WordPress backup integration, and manual override options for edge cases.

= Math / Mermaid not rendered? =
Ensure your theme prints `wp_footer()` and doesn't block the assets.

= How to sync part of a database? =
Use a filtered view in Notion; the plugin respects it.

= What happens on uninstall? =
You can choose to remove all plugin data or keep imported posts.

== Screenshots ==
1. Admin – Sync Dashboard
2. Settings – API & Schedule
3. Front-end – Rendered Content
4. Webhook – Setup Screen

== Chinese Readme (中文说明) ==
完整中文文档请参阅仓库中的 [README-zh_CN.md](https://github.com/Frank-Loong/Notion-to-WordPress/blob/dev/README-zh_CN.md)

== Support & Contribution ==
Issues, PRs, translations welcome!  
GitHub: https://github.com/Frank-Loong/Notion-to-WordPress

== License ==
This plugin is licensed under GPL v3 or later.