<?php
declare(strict_types=1);

namespace NTWP\Handlers;

use NTWP\Core\Logger;
use NTWP\Services\Import\ImportService;

/**
 * 导入处理器
 *
 * 简化的导入处理逻辑，替代复杂的协调器
 *
 * @since      2.0.0-beta.1
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 */
class ImportHandler {
    
    private ImportService $import_service;
    
    public function __construct(ImportService $import_service) {
        $this->import_service = $import_service;
    }
    
    /**
     * 处理导入请求
     *
     * @param array $request 请求数据
     * @return array 处理结果
     */
    public function handle_import_request(array $request): array {
        $database_id = $request['database_id'] ?? '';
        $import_type = $request['type'] ?? 'full';
        
        if (empty($database_id)) {
            return [
                'success' => false,
                'error' => '缺少数据库ID'
            ];
        }
        
        Logger::debug_log(
            sprintf('处理导入请求: %s, 类型: %s', $database_id, $import_type),
            'ImportHandler'
        );
        
        try {
            switch ($import_type) {
                case 'incremental':
                    $since = $request['since'] ?? '';
                    return $this->import_service->execute_incremental_import($database_id, $since);
                
                case 'full':
                default:
                    $options = $request['options'] ?? [];
                    return $this->import_service->execute_full_import($database_id, $options);
            }
            
        } catch (\Exception $e) {
            Logger::debug_log('导入处理异常: ' . $e->getMessage(), 'ImportHandler');
            
            return [
                'success' => false,
                'error' => '导入处理失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取导入状态
     *
     * @return array 状态信息
     */
    public function get_import_status(): array {
        return $this->import_service->get_import_stats();
    }
}
