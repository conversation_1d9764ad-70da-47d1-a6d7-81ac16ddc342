/**
 * LaTeX 数学公式样式
 *
 * 为使用 KaTeX 渲染的 LaTeX 数学公式提供样式，确保在不同设备上正确显示。
 *
 * @since      1.0.7
 * @version    2.0.0-beta.1
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/<PERSON>-<PERSON>/Notion-to-WordPress
 */

/* 行内公式样式 */
.notion-equation-inline {
    display: inline-block;
    vertical-align: middle;
    margin: 0 0.2em;
}

/* 块级公式容器 - 现在直接在第一层标签上有ID和类名 */
.notion-equation-block {
    display: block;
    margin: 0.3em 0 !important; /* 紧凑间距 */
    text-align: center;
    font-size: 1.1em; /* 调整为更接近Notion原版的大小 */
    line-height: 1.4; /* 增加行高，为上下标和大括号留出空间 */
}

/* 如果公式块同时有notion-block类（新的结构） */
.notion-block.notion-equation {
    display: block;
    margin: 0.3em 0 !important; /* 与上面保持一致 */
    text-align: center;
    font-size: 1.1em; /* 调整为更接近Notion原版的大小 */
    line-height: 1.4;
}

/* 覆盖KaTeX默认的katex-display样式 */
.katex-display {
    display: block !important;
    margin: 0.2em 0 !important; /* 覆盖KaTeX默认的1em */
    text-align: center !important;
}

/* 覆盖KaTeX默认的katex样式 */
.katex-display > .katex {
    display: block !important;
    text-align: center !important;
    white-space: nowrap !important;
    font-size: 1em !important; /* 保持正常大小，确保复杂公式显示正确 */
    min-height: 2em; /* 为复杂公式预留足够高度 */
}

/* 确保katex-html有正确的定位上下文和空间预留 */
.katex-display > .katex > .katex-html {
    display: block !important;
    position: relative !important;
    padding-right: 4em !important; /* 为标签预留空间 */
    overflow: visible !important; /* 确保上下标和大括号不被裁剪 */
    min-height: inherit; /* 继承父元素的最小高度 */
}

/* 覆盖KaTeX默认的标签样式 - 学术风格 */
.katex-display > .katex > .katex-html > .tag {
    position: absolute !important;
    right: 0 !important; /* 完全右对齐 */
    top: 50% !important;
    transform: translateY(-50%) !important;

    /* 使用与公式相符的字体样式 */
    font-family: KaTeX_Main, "Times New Roman", serif !important;
    font-size: 0.8em !important; /* 相对于公式的大小 */
    font-weight: normal !important;
    color: inherit !important; /* 继承公式的颜色 */
    line-height: 1.2 !important;

    /* 简洁的学术风格 - 类似传统数学文献 */
    background: none !important;
    border: none !important;
    border-radius: 0 !important;
    padding: 0 0.3em !important; /* 最小padding */
    box-shadow: none !important;
    z-index: 10 !important;

    /* 添加括号样式，符合学术传统 */
    text-decoration: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .notion-equation-block,
    .notion-block.notion-equation {
        margin: 0.2em 0 !important; /* 更紧凑 */
        font-size: 0.9em !important; /* 移动端稍小但不过小 */
        line-height: 1.5; /* 移动端增加行高 */
    }

    .katex-display {
        margin: 0.1em 0 !important;
    }

    .katex-display > .katex {
        font-size: 0.95em !important; /* 移动端稍小但保证可读性 */
        min-height: 1.8em; /* 移动端也预留足够高度 */
    }

    .katex-display > .katex > .katex-html {
        padding-right: 3em !important; /* 减少右边距 */
    }

    .katex-display > .katex > .katex-html > .tag {
        font-size: 0.7em !important;
        padding: 0 0.2em !important; /* 保持简洁 */
    }
}

