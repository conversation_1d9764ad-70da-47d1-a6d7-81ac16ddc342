# Notion to WordPress - Language Files Guide

## File Structure

- `notion-to-wordpress.pot` - Translation template file containing all translatable strings
- `notion-to-wordpress-zh_CN.po` - Simplified Chinese translation file
- `notion-to-wordpress-en_US.po` - English translation file
- `notion-to-wordpress-zh_CN.mo` - Compiled Simplified Chinese translation file (needs to be generated with Poedit)
- `notion-to-wordpress-en_US.mo` - Compiled English translation file (needs to be generated with Poedit)

## Steps to Generate .mo Files Using Poedit

### 1. Install Poedit
Download and install Poedit from https://poedit.net/

### 2. Generate Chinese .mo File
1. Open `notion-to-wordpress-zh_CN.po` with Poedit
2. Check if translations are correct
3. Click "File" -> "Compile to MO" or press Ctrl+M
4. Save as `notion-to-wordpress-zh_CN.mo`

### 3. Generate English .mo File
1. Open `notion-to-wordpress-en_US.po` with Poedit
2. Check if translations are correct
3. Click "File" -> "Compile to MO" or press Ctrl+M
4. Save as `notion-to-wordpress-en_US.mo`

## Language Switching Features

The plugin supports the following language switching methods:

1. **Auto Detection**: Automatically selects the corresponding translation based on the WordPress site language
2. **Force English**: Enable the "Force English Interface" option in the plugin settings

## Adding New Languages

If you need to add support for other languages:

1. Copy the `notion-to-wordpress.pot` file
2. Rename it to `notion-to-wordpress-{language_code}.po` (e.g., `notion-to-wordpress-fr_FR.po`)
3. Open it with Poedit and translate all strings
4. Compile to generate the corresponding `.mo` file

## Updating Translations

When the plugin code is updated with new translatable strings:

1. Update the `.pot` template file
2. Open the existing `.po` files with Poedit
3. Select "Catalog" -> "Update from POT file"
4. Select the updated `.pot` file
5. Translate the new strings
6. Recompile to generate the `.mo` files

## Notes

- All translation files must use UTF-8 encoding
- `.mo` files are binary files that must be generated by compiling `.po` files
- After modifying translations, you need to clear the WordPress cache to see the effects
- The plugin's text domain is `notion-to-wordpress`
