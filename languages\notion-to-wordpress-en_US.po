# English (United States) translation for Notion to WordPress
# Copyright (C) 2025 <PERSON><PERSON><PERSON><PERSON>
# This file is distributed under the same license as the Notion to WordPress package.
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: Notion to WordPress 2.0.0-beta.1\n"
"Report-Msgid-Bugs-To: https://github.com/<PERSON>-<PERSON><PERSON>/Notion-to-WordPress/issues\n"
"POT-Creation-Date: 2025-01-08 17:30+0800\n"
"PO-Revision-Date: 2025-07-09 20:15+0800\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: English (United States)\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"

#: admin\class-notion-to-wordpress-admin.php
msgid "导入中..."
msgstr "Importing..."

#: admin\class-notion-to-wordpress-admin.php
msgid "手动导入"
msgstr "Manual Import"

#: admin\class-notion-to-wordpress-admin.php
msgid "导入过程中发生错误"
msgstr "Error occurred during import"

#: admin\class-notion-to-wordpress-admin.php
msgid "测试中..."
msgstr "Testing..."

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "测试连接"
msgstr "Test Connection"

#: admin\class-notion-to-wordpress-admin.php
msgid "测试连接时发生错误"
msgstr "Error occurred while testing connection"

#: admin\class-notion-to-wordpress-admin.php
msgid "请输入API密钥和数据库ID"
msgstr "Please enter API key and database ID"

#: admin\class-notion-to-wordpress-admin.php
msgid "已复制到剪贴板"
msgstr "Copied to clipboard"

#: admin\class-notion-to-wordpress-admin.php
msgid "刷新中..."
msgstr "Refreshing..."

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "刷新验证令牌"
msgstr "Refresh Verification Token"

#: admin\class-notion-to-wordpress-admin.php
msgid "统计信息错误"
msgstr "Statistics Error"

#: admin\class-notion-to-wordpress-admin.php
msgid "确定要开始同步Notion内容吗？"
msgstr "Are you sure you want to start syncing Notion content?"

#: admin\class-notion-to-wordpress-admin.php
msgid "确定要刷新全部内容吗？这将根据Notion的当前状态重新同步所有页面。"
msgstr "Are you sure you want to refresh all content? This will re-sync all pages based on the current state of Notion."

#: admin\class-notion-to-wordpress-admin.php
msgid "确定要清除所有日志文件吗？此操作不可恢复。"
msgstr "Are you sure you want to clear all log files? This operation cannot be undone."

#: admin\class-notion-to-wordpress-admin.php
msgid "请填写所有必填字段"
msgstr "Please fill in all required fields"

#: admin\class-notion-to-wordpress-admin.php
msgid "隐藏密钥"
msgstr "Hide Key"

#: admin\class-notion-to-wordpress-admin.php
msgid "显示密钥"
msgstr "Show Key"

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "从未"
msgstr "Never"

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "未计划"
msgstr "Not Scheduled"

#: admin\class-notion-to-wordpress-admin.php
msgid "未知错误"
msgstr "Unknown Error"

#: admin\class-notion-to-wordpress-admin.php
msgid "页面ID无效，无法刷新。"
msgstr "Invalid page ID, cannot refresh."

#: admin\class-notion-to-wordpress-admin.php
msgid "安全验证参数缺失，无法继续操作。请刷新页面后重试。"
msgstr "Security verification parameters missing, cannot continue. Please refresh the page and try again."

#: admin\class-notion-to-wordpress-admin.php
msgid "页面已刷新完成！"
msgstr "Page refresh completed!"

#: admin\class-notion-to-wordpress-admin.php
msgid "刷新失败: "
msgstr "Refresh failed: "

#: admin\class-notion-to-wordpress-admin.php
msgid "网络错误，无法刷新页面。"
msgstr "Network error, unable to refresh page."

#: admin\class-notion-to-wordpress-admin.php
msgid "操作超时，请检查该Notion页面内容是否过大。"
msgstr "Operation timed out, please check if the Notion page content is too large."

#: admin\class-notion-to-wordpress-admin.php
msgid "请先选择一个日志文件。"
msgstr "Please select a log file first."

#: admin\class-notion-to-wordpress-admin.php
msgid "正在加载日志..."
msgstr "Loading logs..."

#: admin\class-notion-to-wordpress-admin.php
msgid "无法加载日志: "
msgstr "Unable to load logs: "

#: admin\class-notion-to-wordpress-admin.php
msgid "请求日志时发生错误。"
msgstr "Error occurred while requesting logs."

#: admin\class-notion-to-wordpress-admin.php
msgid "复制失败: 未指定目标元素"
msgstr "Copy failed: Target element not specified"

#: admin\class-notion-to-wordpress-admin.php
msgid "复制失败: 未找到目标元素"
msgstr "Copy failed: Target element not found"

#: admin\class-notion-to-wordpress-admin.php
msgid "复制失败: "
msgstr "Copy failed: "

#: admin\class-notion-to-wordpress-admin.php
msgid "复制到剪贴板"
msgstr "Copy to Clipboard"

#: admin\class-notion-to-wordpress-admin.php
msgid "复制代码"
msgstr "Copy Code"

#: admin\class-notion-to-wordpress-admin.php
msgid "已复制!"
msgstr "Copied!"

#: admin\class-notion-to-wordpress-admin.php
msgid "复制失败，请手动复制。"
msgstr "Copy failed, please copy manually."

#: admin\class-notion-to-wordpress-admin.php
msgid "加载中..."
msgstr "Loading..."

#: admin\class-notion-to-wordpress-admin.php
msgid "加载统计信息..."
msgstr "Loading statistics..."

#: admin\class-notion-to-wordpress-admin.php
msgid "无法加载统计信息"
msgstr "Unable to load statistics"

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "刷新全部内容"
msgstr "Refresh All Content"

#: admin\class-notion-to-wordpress-admin.php
msgid "刷新失败"
msgstr "Refresh Failed"

#: admin\class-notion-to-wordpress-admin.php
msgid "清除中..."
msgstr "Clearing..."

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "清除所有日志"
msgstr "Clear All Logs"

#: admin\class-notion-to-wordpress-admin.php
msgid "设置已保存！"
msgstr "Settings saved!"

#: admin\class-notion-to-wordpress-admin.php
msgid "保存中..."
msgstr "Saving..."

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "智能同步"
msgstr "Smart Sync"

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "完全同步"
msgstr "Full Sync"

#: admin\class-notion-to-wordpress-admin.php
msgid "确定要执行智能同步吗？（仅同步有变化的内容）"
msgstr "Are you sure you want to perform smart sync? (Only sync changed content)"

#: admin\class-notion-to-wordpress-admin.php
msgid "确定要执行完全同步吗？（同步所有内容，耗时较长）"
msgstr "Are you sure you want to perform full sync? (Sync all content, takes longer)"

#: admin\class-notion-to-wordpress-admin.php
msgid "中..."
msgstr "ing..."

#: admin\class-notion-to-wordpress-admin.php
msgid "完成"
msgstr "Completed"

#: admin\class-notion-to-wordpress-admin.php
msgid "失败，请稍后重试"
msgstr "Failed, please try again later"

#: admin\class-notion-to-wordpress-admin.php
msgid "页面即将刷新以应用设置变更..."
msgstr "Page will refresh to apply setting changes..."

#: admin\class-notion-to-wordpress-admin.php
#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion to WordPress"
msgstr "Notion to WordPress"

#: admin\class-notion-to-wordpress-admin.php
msgid "安全验证失败"
msgstr "Security verification failed"

#: admin\class-notion-to-wordpress-admin.php
msgid "权限不足"
msgstr "Insufficient permissions"

#: admin\class-notion-to-wordpress-admin.php
msgid "设置已成功保存。"
msgstr "Settings saved successfully."

#: admin\class-notion-to-wordpress-admin.php
msgid "保存设置时发生错误："
msgstr "Error occurred while saving settings:"

#: admin\class-notion-to-wordpress-admin.php
msgid "导入完成！处理了 %d 个页面，导入了 %d 个页面，更新了 %d 个页面。"
msgstr "Import completed! Processed %d pages, imported %d pages, updated %d pages."

#: admin\class-notion-to-wordpress-admin.php includes\class-notion-pages.php
msgid "导入失败: "
msgstr "Import failed: "

#: admin\class-notion-to-wordpress-admin.php
msgid "请先配置API密钥和数据库ID"
msgstr "Please configure API key and database ID first"

#: admin\class-notion-to-wordpress-admin.php
msgid "刷新完成！处理了 %d 个页面，导入了 %d 个页面，更新了 %d 个页面。"
msgstr "Refresh completed! Processed %d pages, imported %d pages, updated %d pages."

#: admin\class-notion-to-wordpress-admin.php
msgid "未指定日志文件"
msgstr "Log file not specified"

#: admin\class-notion-to-wordpress-admin.php
msgid "验证令牌已刷新"
msgstr "Verification token refreshed"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "🛠️ 主要设置"
msgstr "🛠️ Main Settings"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "🔗 字段映射"
msgstr "🔗 Field Mapping"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "⚙️ 其他设置"
msgstr "⚙️ Other Settings"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "🐞 调试工具"
msgstr "🐞 Debug Tools"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "📖 帮助与指南"
msgstr "📖 Help & Guide"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "👨‍💻 关于作者"
msgstr "👨‍💻 About Author"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion API 设置"
msgstr "Notion API Settings"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "已导入页面"
msgstr "Imported Pages"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "已发布页面"
msgstr "Published Pages"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "最后同步"
msgstr "Last Sync"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "下次同步"
msgstr "Next Sync"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "连接到您的Notion数据库所需的设置。"
msgstr "Settings required to connect to your Notion database."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "了解如何获取API密钥"
msgstr "Learn how to get API key"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "API密钥"
msgstr "API Key"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "输入您的Notion API密钥"
msgstr "Enter your Notion API key"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "显示/隐藏密钥"
msgstr "Show/Hide Key"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "数据库ID"
msgstr "Database ID"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "输入您的Notion数据库ID"
msgstr "Enter your Notion database ID"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "可以从Notion数据库URL中找到，格式如：https://www.notion.so/xxx/<strong>数据库ID</strong>?v=xxx"
msgstr "Can be found in the Notion database URL, format: https://www.notion.so/xxx/<strong>Database ID</strong>?v=xxx"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "自动同步频率"
msgstr "Auto Sync Frequency"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "手动同步"
msgstr "Manual Sync"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "每天两次"
msgstr "Twice Daily"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "每天一次"
msgstr "Daily"

#: admin\partials\notion-to-wordpress-admin-display.php
#: includes\class-notion-to-wordpress.php
msgid "每周一次"
msgstr "Weekly"

#: admin\partials\notion-to-wordpress-admin-display.php
#: includes\class-notion-to-wordpress.php
msgid "每两周一次"
msgstr "Bi-weekly"

#: admin\partials\notion-to-wordpress-admin-display.php
#: includes\class-notion-to-wordpress.php
msgid "每月一次"
msgstr "Monthly"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "定时同步选项"
msgstr "Scheduled Sync Options"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "启用增量同步"
msgstr "Enable Incremental Sync"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "仅同步有变化的页面，提高同步速度"
msgstr "Only sync changed pages to improve sync speed"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "检查删除的页面"
msgstr "Check Deleted Pages"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "自动删除在Notion中已删除但WordPress中仍存在的文章"
msgstr "Automatically delete articles that have been deleted in Notion but still exist in WordPress"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Webhook 支持"
msgstr "Webhook Support"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "启用 Webhook 支持"
msgstr "Enable Webhook Support"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "启用后，您可以设置 Notion 集成的 Webhook 以在内容变更时自动触发同步。"
msgstr "When enabled, you can set up Notion integration Webhook to automatically trigger sync when content changes."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "验证令牌"
msgstr "Verification Token"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "等待 Notion 返回…"
msgstr "Waiting for Notion response..."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "复制令牌"
msgstr "Copy Token"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "首次发送 Webhook 时，Notion 将返回 verification_token，此处会自动展示。点击刷新按钮可获取最新的令牌。"
msgstr "When sending Webhook for the first time, Notion will return verification_token, which will be automatically displayed here. Click the refresh button to get the latest token."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Webhook 地址"
msgstr "Webhook URL"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "复制 URL"
msgstr "Copy URL"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在 Notion 开发者平台设置此 URL 作为您集成的 Webhook 终端点。"
msgstr "Set this URL as your integration's Webhook endpoint in the Notion developer platform."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Webhook 同步选项"
msgstr "Webhook Sync Options"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Webhook触发时仅同步有变化的页面，提高响应速度"
msgstr "Only sync changed pages when Webhook is triggered to improve response speed"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "数据库事件检查删除"
msgstr "Database Event Check Deletion"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "数据库结构变化时检查删除的页面（单页面事件不受影响）"
msgstr "Check deleted pages when database structure changes (single page events are not affected)"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "字段映射"
msgstr "Field Mapping"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "设置您的Notion数据库属性名称与WordPress字段的对应关系。多个备选名称请用英文逗号隔开。"
msgstr "Set the correspondence between your Notion database property names and WordPress fields. Separate multiple alternative names with commas."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "文章标题"
msgstr "Post Title"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章标题的Notion属性名称"
msgstr "Notion property name used to set WordPress post title"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "状态"
msgstr "Status"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "文章类型"
msgstr "Post Type"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于确定WordPress文章类型的Notion属性名称"
msgstr "Notion property name used to determine WordPress post type"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "日期"
msgstr "Date"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章发布日期的Notion属性名称"
msgstr "Notion property name used to set WordPress post publication date"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "摘要"
msgstr "Excerpt"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章摘要的Notion属性名称"
msgstr "Notion property name used to set WordPress post excerpt"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "特色图片"
msgstr "Featured Image"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress特色图片的Notion属性名称（应为URL或文件类型）"
msgstr "Notion property name used to set WordPress featured image (should be URL or file type)"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "分类"
msgstr "Categories"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章分类的Notion属性名称"
msgstr "Notion property name used to set WordPress post categories"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "标签"
msgstr "Tags"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章标签的Notion属性名称"
msgstr "Notion property name used to set WordPress post tags"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "文章密码"
msgstr "Post Password"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "用于设置WordPress文章密码的Notion属性名称"
msgstr "Notion property name used to set WordPress post password"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "自定义字段映射"
msgstr "Custom Field Mapping"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "将Notion属性映射到WordPress自定义字段。您可以添加任意数量的自定义字段映射。"
msgstr "Map Notion properties to WordPress custom fields. You can add any number of custom field mappings."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "文本"
msgstr "Text"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "数字"
msgstr "Number"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "复选框"
msgstr "Checkbox"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "选择"
msgstr "Select"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "多选"
msgstr "Multi-select"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "URL"
msgstr "URL"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "电子邮件"
msgstr "Email"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "电话"
msgstr "Phone"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "富文本"
msgstr "Rich Text"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion属性名称"
msgstr "Notion Property Name"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "例如：Author,作者"
msgstr "e.g.: Author,作者"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion中的属性名称，多个备选名称请用英文逗号分隔"
msgstr "Property name in Notion, separate multiple alternative names with commas"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "WordPress字段名称"
msgstr "WordPress Field Name"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "例如：author"
msgstr "e.g.: author"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "WordPress中的自定义字段名称"
msgstr "Custom field name in WordPress"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "字段类型"
msgstr "Field Type"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion属性的数据类型"
msgstr "Data type of Notion property"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "添加自定义字段"
msgstr "Add Custom Field"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "其他设置"
msgstr "Other Settings"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "卸载设置"
msgstr "Uninstall Settings"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "卸载时删除所有同步内容"
msgstr "Delete all synced content when uninstalling"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "卸载插件时，删除所有从Notion同步的文章和页面"
msgstr "When uninstalling the plugin, delete all articles and pages synced from Notion"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "警告：此操作不可逆！所有通过Notion同步的内容将被永久删除。"
msgstr "Warning: This operation is irreversible! All content synced through Notion will be permanently deleted."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "iframe 白名单域名"
msgstr "iframe Whitelist Domains"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "允许在内容中嵌入的 iframe 域名白名单，多个域名请用英文逗号分隔。输入 * 表示允许所有域名（不推荐）。"
msgstr "Whitelist of iframe domains allowed to be embedded in content. Separate multiple domains with commas. Enter * to allow all domains (not recommended)."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "允许的图片格式"
msgstr "Allowed Image Formats"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "允许下载和导入的图片 MIME 类型，多个类型请用英文逗号分隔。输入 * 表示允许所有格式。"
msgstr "MIME types of images allowed to be downloaded and imported. Separate multiple types with commas. Enter * to allow all formats."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "插件界面语言"
msgstr "Plugin Interface Language"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "自动检测（跟随站点语言）"
msgstr "Auto Detect (Follow Site Language)"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "简体中文"
msgstr "Simplified Chinese"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "English"
msgstr "English"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "选择插件界面显示的语言。自动检测将跟随WordPress站点语言设置。"
msgstr "Select the language for the plugin interface. Auto detect will follow WordPress site language settings."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "最大图片大小"
msgstr "Maximum Image Size"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "MB"
msgstr "MB"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "允许下载的最大图片大小（以 MB 为单位）。建议不超过 10MB。"
msgstr "Maximum image size allowed for download (in MB). Recommended not to exceed 10MB."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "调试工具"
msgstr "Debug Tools"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在这里，您可以管理日志级别、查看和清除日志文件。"
msgstr "Here you can manage log levels, view and clear log files."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "日志记录级别"
msgstr "Log Level"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "无日志"
msgstr "No Logs"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "仅错误"
msgstr "Errors Only"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "信息和错误"
msgstr "Info and Errors"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "所有日志 (调试)"
msgstr "All Logs (Debug)"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "日志保留期限"
msgstr "Log Retention Period"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "从不自动清理"
msgstr "Never Auto Clean"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "7 天"
msgstr "7 Days"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "14 天"
msgstr "14 Days"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "30 天"
msgstr "30 Days"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "60 天"
msgstr "60 Days"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "错误日志"
msgstr "Error Logs"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "查看日志"
msgstr "View Logs"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "使用帮助"
msgstr "Usage Help"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "快速开始"
msgstr "Quick Start"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在Notion创建一个集成并获取API密钥"
msgstr "Create an integration in Notion and get the API key"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在Notion中创建一个数据库，并与您的集成共享"
msgstr "Create a database in Notion and share it with your integration"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "复制数据库ID（从URL中获取）"
msgstr "Copy the database ID (get it from the URL)"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在此页面配置API密钥和数据库ID"
msgstr "Configure the API key and database ID on this page"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "配置字段映射，确保Notion属性名称与WordPress字段正确对应"
msgstr "Configure field mapping to ensure Notion property names correspond correctly with WordPress fields"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "常见问题"
msgstr "FAQ"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "问：为什么我的Notion页面没有导入？"
msgstr "Q: Why are my Notion pages not imported?"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "答：请检查以下几点："
msgstr "A: Please check the following:"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "确认您的API密钥和数据库ID正确"
msgstr "Confirm that your API key and database ID are correct"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "确认您的Notion集成已与数据库共享"
msgstr "Confirm that your Notion integration has been shared with the database"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "检查字段映射是否正确对应Notion中的属性名称"
msgstr "Check if field mapping correctly corresponds to property names in Notion"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "问：如何自定义导入的内容格式？"
msgstr "Q: How to customize the format of imported content?"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "答：本插件会尽可能保留Notion中的格式，包括标题、列表、表格、代码块等。对于特殊内容（如数学公式、图表），插件也提供了支持。"
msgstr "A: This plugin will preserve the format in Notion as much as possible, including headings, lists, tables, code blocks, etc. For special content (such as mathematical formulas, charts), the plugin also provides support."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "问：导入后如何更新内容？"
msgstr "Q: How to update content after import?"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "获取支持"
msgstr "Get Support"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "如果您遇到任何问题或需要帮助，请访问我们的GitHub仓库："
msgstr "If you encounter any problems or need help, please visit our GitHub repository:"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "关于作者"
msgstr "About Author"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "科技爱好者 & AI玩家"
msgstr "Tech Enthusiast & AI Player"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "对互联网、计算机等科技行业充满热情，擅长 AI 工具的使用与调教。"
msgstr "Passionate about the Internet, computers and other technology industries, skilled in the use and training of AI tools."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "此插件在强大的 AI 编程助手 Cursor 和 Augment 的协助下完成，现在将这个有趣的项目分享给大家。"
msgstr "This plugin was completed with the assistance of powerful AI programming assistants Cursor and Augment, and now I share this interesting project with everyone."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "个人网站"
msgstr "Personal Website"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "联系邮箱"
msgstr "Contact Email"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "插件信息"
msgstr "Plugin Information"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "版本："
msgstr "Version:"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "许可证："
msgstr "License:"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "兼容性："
msgstr "Compatibility:"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "致谢与参考"
msgstr "Acknowledgments & References"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "本项目的开发过程中参考了以下优秀的开源项目："
msgstr "The following excellent open source projects were referenced during the development of this project:"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "基于 Notion 的强大静态博客系统"
msgstr "Powerful static blog system based on Notion"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "支持多平台的开源博客写作客户端"
msgstr "Open source blog writing client supporting multiple platforms"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "Notion 内容管理解决方案"
msgstr "Notion content management solution"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "感谢这些项目及其维护者对开源社区的贡献！"
msgstr "Thanks to these projects and their maintainers for their contributions to the open source community!"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "只同步有变化的页面，速度更快"
msgstr "Only sync changed pages, faster speed"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "同步所有页面，确保数据一致性"
msgstr "Sync all pages to ensure data consistency"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "保存所有设置"
msgstr "Save All Settings"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "处理中，请稍候..."
msgstr "Processing, please wait..."

#: includes\class-notion-api.php
msgid "API请求失败: "
msgstr "API request failed: "

#: includes\class-notion-api.php
msgid "API错误 ("
msgstr "API error ("

#: includes\class-notion-api.php
msgid "连接测试失败: "
msgstr "Connection test failed: "

#: includes\class-notion-pages.php
msgid "此为 Notion 临时图片链接，可能会过期。请考虑替换为图床或本地媒体库图片。"
msgstr "This is a temporary Notion image link that may expire. Please consider replacing it with an image hosting service or local media library image."

#: includes\class-notion-pages.php
msgid "未检索到任何页面。"
msgstr "No pages retrieved."

#: includes\class-notion-pages.php
msgid "下载附件"
msgstr "Download Attachment"

#: includes\class-notion-pages.php
msgid "下载 PDF"
msgstr "Download PDF"

#: includes\class-notion-pages.php
msgid "在新窗口打开"
msgstr "Open in New Window"

#: includes\class-notion-to-wordpress-webhook.php
msgid "Token mismatch"
msgstr "Token mismatch"

#: includes\class-notion-to-wordpress-webhook.php
msgid "无效的请求体"
msgstr "Invalid request body"

#: includes\class-notion-to-wordpress-webhook.php
msgid "缺少事件类型"
msgstr "Missing event type"

#: includes\class-notion-to-wordpress-webhook.php
msgid "Webhook已接收，正在处理"
msgstr "Webhook received, processing"

#: includes\class-notion-to-wordpress-webhook.php
msgid "同步过程中出错: "
msgstr "Error during sync: "

#: includes\class-notion-to-wordpress-webhook.php
msgid "已触发%s"
msgstr "Triggered %s"

#: includes\class-notion-to-wordpress-webhook.php
msgid "页面ID为空，无法处理删除事件"
msgstr "Page ID is empty, cannot process delete event"

#: includes\class-notion-to-wordpress-webhook.php
msgid "未找到对应的WordPress文章"
msgstr "Corresponding WordPress post not found"

#: includes\class-notion-to-wordpress-webhook.php
msgid "已删除对应的WordPress文章 (ID: %d)"
msgstr "Deleted corresponding WordPress post (ID: %d)"

#: includes\class-notion-to-wordpress-webhook.php
msgid "删除WordPress文章失败"
msgstr "Failed to delete WordPress post"

#: includes\class-notion-to-wordpress-webhook.php
msgid "已同步页面: %s"
msgstr "Synced page: %s"

#: includes\class-notion-to-wordpress-webhook.php
msgid "页面同步失败"
msgstr "Page sync failed"

#: includes\class-notion-to-wordpress-webhook.php
msgid "单页面同步失败，已执行%s"
msgstr "Single page sync failed, executed %s"

#: includes\class-notion-to-wordpress-webhook.php
msgid "Notion Pages对象不存在"
msgstr "Notion Pages object does not exist"

#: includes\class-notion-to-wordpress-webhook.php
msgid "获取页面数据失败"
msgstr "Failed to get page data"

#: includes\class-notion-to-wordpress-webhook.php
msgid "已强制同步页面内容: %s"
msgstr "Force synced page content: %s"

#: includes\class-notion-to-wordpress-webhook.php
msgid "页面内容同步失败"
msgstr "Page content sync failed"

#: includes\class-notion-to-wordpress-webhook.php
msgid "页面内容同步失败，已执行%s"
msgstr "Page content sync failed, executed %s"

#: includes\class-notion-to-wordpress-webhook.php
msgid "页面内容同步发生致命错误"
msgstr "Fatal error occurred during page content sync"

#: includes\class-notion-to-wordpress-webhook.php
msgid "数据库已更新，已触发%s%s"
msgstr "Database updated, triggered %s%s"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "设置日志记录的详细程度。建议在生产环境中设置为\"仅错误\"。"
msgstr "Set the log recording detail level. Recommended to set to \"Errors Only\" in production environment."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "自动删除超过指定天数的旧日志文件。设置为\"从不\"以禁用。"
msgstr "Automatically delete old log files older than specified days. Set to \"Never\" to disable."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "点击\"测试连接\"确认设置正确"
msgstr "Click \"Test Connection\" to confirm settings are correct"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "点击\"保存所有设置\"保存您的配置"
msgstr "Click \"Save All Settings\" to save your configuration"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "点击\"手动同步\"或设置自动同步频率开始导入内容"
msgstr "Click \"Manual Sync\" or set auto sync frequency to start importing content"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "尝试使用\"刷新全部内容\"按钮重新同步"
msgstr "Try using the \"Refresh All Content\" button to re-sync"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "答：当您在Notion中更新内容后，可以点击\"刷新全部内容\"按钮手动更新，或等待自动同步（如果已设置）。"
msgstr "A: After updating content in Notion, you can click the \"Refresh All Content\" button to manually update, or wait for automatic sync (if configured)."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "选择 \"手动同步\" 以禁用定时任务。"
msgstr "Select \"Manual Sync\" to disable scheduled tasks."

#: admin\class-notion-to-wordpress-admin.php
msgid "要复制的文本为空"
msgstr "Text to copy is empty"

#: admin\class-notion-to-wordpress-admin.php
msgid "暂无新的验证令牌"
msgstr "No new verification token available"

#: admin\class-notion-to-wordpress-admin.php
msgid "连接成功！数据库可访问。"
msgstr "Connection successful! Database is accessible."

#: admin\class-notion-to-wordpress-admin.php
msgid "连接失败: "
msgstr "Connection failed: "

#: admin\class-notion-to-wordpress-admin.php
msgid "缺少nonce参数"
msgstr "Missing nonce parameter"

#: admin\class-notion-to-wordpress-admin.php
msgid "nonce验证失败"
msgstr "Nonce verification failed"

#: admin\class-notion-to-wordpress-admin.php
msgid "Nonce验证失败"
msgstr "Nonce verification failed"

#: admin\class-notion-to-wordpress-admin.php
msgid "获取统计信息失败: "
msgstr "Failed to get statistics: "

#: admin\class-notion-to-wordpress-admin.php
msgid "获取统计信息错误: "
msgstr "Error getting statistics: "

#: admin\class-notion-to-wordpress-admin.php
msgid "所有日志文件已清除"
msgstr "All log files have been cleared"

#: admin\class-notion-to-wordpress-admin.php
msgid "清除日志时出现错误"
msgstr "Error occurred while clearing logs"

#: admin\class-notion-to-wordpress-admin.php
msgid "清除日志失败: "
msgstr "Failed to clear logs: "

#: admin\class-notion-to-wordpress-admin.php
msgid "无效"
msgstr "Invalid"

#: admin\class-notion-to-wordpress-admin.php
msgid "不存在"
msgstr "Does not exist"

#: admin\class-notion-to-wordpress-admin.php
msgid "调试测试成功"
msgstr "Debug test successful"

#: admin\class-notion-to-wordpress-admin.php
msgid "测试失败: "
msgstr "Test failed: "

#: admin\class-notion-to-wordpress-admin.php
msgid "测试错误: "
msgstr "Test error: "

#: includes\class-notion-pages.php
msgid "无效的嵌入URL"
msgstr "Invalid embed URL"

#: includes\class-notion-pages.php
msgid "无效的视频URL"
msgstr "Invalid video URL"

#: includes\class-notion-pages.php
msgid "您的浏览器不支持视频标签。"
msgstr "Your browser does not support the video tag."

#: includes\class-notion-pages.php
msgid "查看视频"
msgstr "View Video"

#: includes\class-notion-pages.php
msgid "不允许的图片类型："
msgstr "Disallowed image type: "

#: includes\class-notion-pages.php
msgid "图片文件过大（%sMB），超过限制（%sMB）"
msgstr "Image file too large (%sMB), exceeds limit (%sMB)"

#: includes\class-notion-pages.php
msgid "无效的 PDF URL"
msgstr "Invalid PDF URL"

#: includes\class-notion-pages.php
msgid "您的浏览器不支持PDF预览。"
msgstr "Your browser does not support PDF preview."

#: includes\class-notion-pages.php
msgid "点击下载PDF文件"
msgstr "Click to download PDF file"

#: includes\class-notion-pages.php
msgid "PDF 下载失败"
msgstr "PDF download failed"

#: includes\class-notion-pages.php
msgid "下载附件失败: "
msgstr "Failed to download attachment: "

#: includes\class-notion-pages.php
msgid "无效的PDF文件或包含不安全内容"
msgstr "Invalid PDF file or contains unsafe content"

#: includes\class-notion-pages.php
msgid "media_handle_sideload 错误: "
msgstr "media_handle_sideload error: "

#: includes\class-notion-to-wordpress-helper.php
msgid "内容已截断，完整内容请查看专用日志文件"
msgstr "Content truncated, see dedicated log file for full content"

#: includes\class-notion-to-wordpress-helper.php
msgid "HTML标签已过滤"
msgstr "HTML tags filtered"

#: includes\class-notion-to-wordpress-helper.php
msgid "HTML内容已过滤"
msgstr "HTML content filtered"

#: includes\class-notion-to-wordpress-helper.php
msgid "JSON响应已过滤，长度: %d 字符"
msgstr "JSON response filtered, length: %d characters"

#: includes\class-notion-to-wordpress-helper.php
msgid "数组内容已过滤，长度: %d 字符"
msgstr "Array content filtered, length: %d characters"

#: includes\class-notion-to-wordpress-helper.php
msgid "无效的文件名。"
msgstr "Invalid filename."

#: includes\class-notion-to-wordpress-helper.php
msgid "日志文件不存在。"
msgstr "Log file does not exist."

#: includes\class-notion-to-wordpress-helper.php
msgid "日志清理任务跳过：未设置保留期限。"
msgstr "Log cleanup task skipped: retention period not set."

#: includes\class-notion-to-wordpress-helper.php
msgid "日志清理完成，删除了 %d 个旧日志文件。"
msgstr "Log cleanup completed, deleted %d old log files."

#: includes\class-notion-to-wordpress-helper.php
msgid "日志清理任务运行，没有需要删除的文件。"
msgstr "Log cleanup task ran, no files to delete."

#: includes\class-notion-to-wordpress-helper.php
msgid "无效的URL"
msgstr "Invalid URL"

#: includes\class-notion-to-wordpress-helper.php
msgid "远程请求失败: "
msgstr "Remote request failed: "

#: includes\class-notion-to-wordpress-helper.php
msgid "HTTP错误 %d: %s"
msgstr "HTTP error %d: %s"

#: admin\class-notion-to-wordpress-admin.php
msgid "详细信息"
msgstr "Details"

#: admin\class-notion-to-wordpress-admin.php
msgid "验证令牌已更新"
msgstr "Verification token updated"

#: admin\class-notion-to-wordpress-admin.php
msgid "语言设置"
msgstr "Language Settings"

#: admin\class-notion-to-wordpress-admin.php
msgid "Webhook设置"
msgstr "Webhook Settings"

#: admin\class-notion-to-wordpress-admin.php
msgid "和"
msgstr "and"

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "在Notion的\"我的集成\"页面创建并获取API密钥。"
msgstr "Create and get API key in Notion's \"My integrations\" page."

#: admin\partials\notion-to-wordpress-admin-display.php
msgid "值为 \"Published\" 或 \"已发布\" 的页面会被设为 \"已发布\" 状态，其他则为 \"草稿\"。"
msgstr "Pages with value \"Published\" or \"已发布\" will be set to \"Published\" status, others will be \"Draft\"."

#: includes\class-notion-pages.php
msgid "暂无记录"
msgstr "No records"

#: includes\class-notion-pages.php
msgid "无标题"
msgstr "Untitled"

#: includes\class-notion-pages.php
msgid "封面图片"
msgstr "Cover image"

#: includes\class-notion-pages.php
msgid "图标"
msgstr "Icon"

#: includes\class-notion-pages.php
msgid "数据库记录封面"
msgstr "Database record cover"

#: includes\class-notion-pages.php
msgid "数据库记录图标"
msgstr "Database record icon"

#: includes\class-notion-pages.php
msgid "加载更多记录 (%d)"
msgstr "Load more records (%d)"

#: includes\class-notion-pages.php
msgid "标题"
msgstr "Title"

#: includes\class-notion-pages.php
msgid "记录ID不能为空"
msgstr "Record ID cannot be empty"

#: includes\class-notion-pages.php
msgid "无法获取记录详情"
msgstr "Unable to get record details"

#: includes\class-notion-pages.php
msgid "获取记录详情失败: %s"
msgstr "Failed to get record details: %s"
