<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store功能验证测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .test-section h2 {
            margin-top: 0;
            color: #555;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-passed {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-failed {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .run-button {
            display: block;
            width: 200px;
            margin: 20px auto;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .run-button:hover {
            background-color: #0056b3;
        }
        .run-button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Zustand Store 功能验证测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>此页面用于验证新实现的Zustand状态管理架构的基本功能。测试包括：</p>
            <ul>
                <li><strong>SyncStore</strong>: 同步状态管理、进度更新、错误处理</li>
                <li><strong>SettingsStore</strong>: 设置管理、字段验证、连接测试</li>
                <li><strong>UIStore</strong>: UI状态、通知系统、主题切换</li>
                <li><strong>复合功能</strong>: 跨store操作、应用初始化、状态重置</li>
            </ul>
        </div>

        <button id="runTest" class="run-button">🚀 运行Store验证测试</button>
        
        <div id="testResults"></div>
        
        <div class="instructions">
            <h3>🔧 手动测试步骤</h3>
            <p>如果自动测试无法运行，可以在浏览器控制台中手动执行以下代码：</p>
            <div class="code-block">
// 1. 测试SyncStore基本功能<br>
const syncStore = window.useSyncStore?.getState();<br>
console.log('SyncStore初始状态:', syncStore);<br>
<br>
// 2. 测试SettingsStore基本功能<br>
const settingsStore = window.useSettingsStore?.getState();<br>
console.log('SettingsStore初始状态:', settingsStore);<br>
<br>
// 3. 测试UIStore基本功能<br>
const uiStore = window.useUIStore?.getState();<br>
console.log('UIStore初始状态:', uiStore);<br>
<br>
// 4. 运行完整验证<br>
if (window.runStoreValidation) {<br>
&nbsp;&nbsp;window.runStoreValidation();<br>
} else {<br>
&nbsp;&nbsp;console.log('验证函数未加载');<br>
}
            </div>
        </div>
    </div>

    <script type="module">
        // 模拟导入store（实际项目中会通过构建工具处理）
        let storesLoaded = false;
        let validationFunction = null;

        // 尝试加载stores
        async function loadStores() {
            try {
                // 这里需要实际的构建输出或者开发服务器
                console.log('⚠️ 注意: 此测试页面需要在Vite开发服务器环境中运行');
                console.log('请运行: npm run dev 然后访问 http://localhost:5173/store-test.html');
                
                // 检查是否在开发环境中
                if (window.location.protocol === 'file:') {
                    throw new Error('请在HTTP服务器环境中运行此测试');
                }
                
                // 尝试动态导入
                const { runStoreValidation } = await import('./src/stores/__test__/store-validation.ts');
                validationFunction = runStoreValidation;
                storesLoaded = true;
                
                document.getElementById('testResults').innerHTML = `
                    <div class="success">
                        ✅ Store模块加载成功！可以开始测试。
                    </div>
                `;
                
            } catch (error) {
                console.error('加载stores失败:', error);
                document.getElementById('testResults').innerHTML = `
                    <div class="error">
                        <h3>❌ 无法加载Store模块</h3>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                        <p><strong>解决方案:</strong></p>
                        <ol>
                            <li>确保在项目根目录运行 <code>cd frontend && npm run dev</code></li>
                            <li>在浏览器中访问 <code>http://localhost:5173/store-test.html</code></li>
                            <li>或者在浏览器控制台中手动测试（参考下方说明）</li>
                        </ol>
                    </div>
                `;
            }
        }

        // 运行测试
        async function runTests() {
            const button = document.getElementById('runTest');
            const resultsDiv = document.getElementById('testResults');
            
            button.disabled = true;
            button.textContent = '🔄 运行中...';
            
            resultsDiv.innerHTML = '<div class="loading">正在运行测试...</div>';
            
            try {
                if (!storesLoaded || !validationFunction) {
                    throw new Error('Store模块未正确加载');
                }
                
                const results = validationFunction();
                
                let html = '<h2>📊 测试结果</h2>';
                
                results.forEach(result => {
                    const statusClass = result.overallPassed ? 'test-passed' : 'test-failed';
                    const statusIcon = result.overallPassed ? '✅' : '❌';
                    
                    html += `
                        <div class="test-section">
                            <h3>${statusIcon} ${result.storeName}</h3>
                    `;
                    
                    result.tests.forEach(test => {
                        const testClass = test.passed ? 'test-passed' : 'test-failed';
                        const testIcon = test.passed ? '✅' : '❌';
                        html += `
                            <div class="test-result ${testClass}">
                                ${testIcon} ${test.name}
                                ${test.error ? `<br><small>错误: ${test.error}</small>` : ''}
                            </div>
                        `;
                    });
                    
                    html += '</div>';
                });
                
                const overallPassed = results.every(result => result.overallPassed);
                const summaryClass = overallPassed ? 'success' : 'error';
                const summaryIcon = overallPassed ? '🎉' : '⚠️';
                
                html += `
                    <div class="${summaryClass}">
                        <h3>${summaryIcon} 总体结果</h3>
                        <p>${overallPassed ? '所有测试通过！Store架构工作正常。' : '部分测试失败，需要检查相关功能。'}</p>
                    </div>
                `;
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ 测试执行失败</h3>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                    </div>
                `;
            } finally {
                button.disabled = false;
                button.textContent = '🚀 运行Store验证测试';
            }
        }

        // 事件监听
        document.getElementById('runTest').addEventListener('click', runTests);
        
        // 页面加载时尝试加载stores
        loadStores();
    </script>
</body>
</html>
