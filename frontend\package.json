{"name": "notion-to-wordpress-frontend", "version": "2.0.0-beta.1", "description": "React frontend for Notion-to-WordPress plugin", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"axios": "^1.4.0", "clsx": "^2.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "zustand": "^4.4.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "typescript": "^5.0.4", "vite": "^4.4.0"}, "engines": {"node": ">=16.0.0"}}