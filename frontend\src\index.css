@tailwind base;
@tailwind components;
@tailwind utilities;

/* WordPress后台兼容样式 */
.notion-to-wordpress-react-app {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  line-height: 1.4;
}

/* 确保React应用不影响WordPress后台样式 */
.notion-to-wordpress-react-app * {
  box-sizing: border-box;
}

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition-colors;
  }
  
  .card {
    @apply bg-white border border-gray-200 rounded-lg shadow-sm p-4;
  }
}